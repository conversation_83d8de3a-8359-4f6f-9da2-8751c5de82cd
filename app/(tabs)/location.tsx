import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { ScrollView } from "@/components/ui/scroll-view";
import { SafeAreaView } from "react-native-safe-area-context";
import ClassesHeader from "@/components/screens/classes/classes-header";
import HorizontalDatePicker from "@/components/shared/horizontal-date-picker";
import ClassesTabs from "@/components/screens/classes/classes-tabs";
import ClassCard from "@/components/screens/classes/class-card";

import { SearchInput } from "@/components/screens/classes/classes-header/search";
import { FlatList } from "react-native";
import { useClassesQuery } from "@/data/screens/location/queries/useClassesQuery";

export const Classes = () => {
  const [selectedTab, setSelectedTab] = useState<"classes" | "appointment">(
    "classes"
  );
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data } = useClassesQuery({ date: selectedDate });

  console.log(data);

  return (
    <SafeAreaView className="flex-1 bg-white">
      <VStack className="flex-1">
        <ClassesHeader />
        <ClassesTabs selectedTab={selectedTab} onTabSelect={setSelectedTab} />
        <VStack space="md" className="pb-6">
          <HorizontalDatePicker
            selectedDate={selectedDate}
            onDateSelect={setSelectedDate}
          />
          <SearchInput />

          <VStack space="sm" className="px-4">
            <FlatList
              data={data}
              renderItem={({ item }) => <ClassCard key={item.id} {...item} />}
              keyExtractor={(item) => String(item.id)}
            />
          </VStack>
        </VStack>
      </VStack>
    </SafeAreaView>
  );
};

export default Classes;
